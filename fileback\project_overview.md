实验教学管理系统 - 项目总体规划文档
一、项目基本信息
项目名称：实验教学管理系统 (GCQETS - General Chemistry Quality Education Teaching System)  
项目目标：构建一个支持五级权限管理的实验教学综合管理平台  
开发模式：AI辅助开发  
文档版本：v1.0  
最后更新：2025-06-16  
二、系统架构概述
（一）技术架构
前端层 (Frontend)
├── Vue 3.3.4 + Composition API
├── Vue Router 4.2.4 (路由管理)
├── Pinia 2.1.6 (状态管理)
├── Element Plus 2.9.5 (UI组件库)
├── Axios 1.4.0 (HTTP客户端)
└── Vite 4.5.9 (构建工具)

后端层 (Backend)
├── PHP 8.2.12
├── Laravel 12.0.1 (Web框架)
├── MySQL/MariaDB 10.4.32 (数据库)
└── Composer 2.6.10 (依赖管理)

项目结构
F:\xampp\htdocs\gcqets
├── frontend/   (前端项目)
└── backend/    (后端项目)

（二）系统分层架构
表现层 (Presentation Layer)
├── 用户界面 (Vue.js Components)
├── 路由管理 (Vue Router)
└── 状态管理 (Pinia Store)

业务层 (Business Layer)
├── 控制器 (Laravel Controllers)
├── 服务层 (Service Classes)
├── 中间件 (Middleware)
└── 权限验证 (Authorization)

数据层 (Data Layer)
├── 模型 (Eloquent Models)
├── 数据迁移 (Migrations)
├── 数据填充 (Seeders)
└── 数据库 (MySQL/MariaDB)

三、功能模块划分

（一）主要功能模块：
模块一：组织机构管理
模块二：系统管理
模块三：实验过程管理
模块四：基础数据管理
模块五：数据看板
模块六：督导管理
模块七：资源共享
模块八：用户反馈
模块九：通知管理
模块十：数据备份与恢复
模块十一：智能辅助
模块十二：移动端扩展

（二）基础支撑模块
 1. 组织机构管理模块 (优先开发)
  **单位组织管理**
  - 五级行政单位管理
  - 组织架构维护
  - 上下级关系管理
- **用户管理**
  - 用户注册、登录、注销
  - 用户信息维护
  - 密码管理
- **角色权限管理**
  - 五级权限体系
  - 角色定义和分配
  - 权限矩阵管理
-

    ### 功能一：组织管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **组织列表**       | 省/市/区县/学区管理员     | 查看管理范围内的组织树，支持五级结构展示      |
| **新增组织**       | 省/市/区县/学区管理员     | 上级可创建下级单位，自动继承层级关系          |
| **编辑组织**       | 省/市/区县/学区管理员     | 修改组织信息并标记直属关系                  |
| **删除组织**       | 省/市/区县/学区管理员     | 级联删除需验证下级单位为空                  |
| **权限继承设置**   | 省/市/区县管理员          | 配置五级权限继承链：省→市→区县→学区→学校    |
| **批量导入**       | 省/市/区县/学区管理员     | 支持Excel导入多级单位，模板含五级层级字段   |
| **区域划分管理**   | 省/市/区县管理员          | 跨层级调整管辖关系，支持拖拽式区域划分      |

### 功能二：用户管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **用户列表**       | 省/市/区县/学区/学校管理员| 按管辖范围筛选用户                   |
| **新增用户**       | 省/市/区县/学区/学校管理员| 用户自动继承组织层级属性             |
| **编辑用户**       | 省/市/区县/学区/学校管理员| 修改用户信息及权限范围              |
| **删除用户**       | 省/市/区县/学区/学校管理员| 删除用户账户                          |
| **用户导入**       | 省/市/区县/学区管理员     | 批量导入用户并分配组织                |

### 功能三：角色管理
| 子功能             | 适用角色              | 功能说明                                  |
|--------------------|-----------------------|-----------------------------------------|
| **角色列表**       | 省/市/区县管理员      | 查看系统角色及其权限范围                |
| **新增角色**       | 省/市/区县管理员      | 创建新角色并指定适用层级                 |
| **编辑角色**       | 省/市/区县管理员      | 修改角色权限设置                        |
| **删除角色**       | 省/市/区县管理员      | 删除不需要的角色                        |
| **权限设置**       | 省/市/区县管理员      | 按层级分配数据权限                      |
| **权限继承设置**   | 省/市/区县管理员      | 配置跨层级权限继承关系                   |
| **权限撤销设置**   | 省/市/区县管理员      | 撤销特定层级的权限                      |

## 权限控制矩阵

| 操作权限                 | 省级 | 市级 | 区县级 | 学区级 | 校级 |
|--------------------------|------|------|--------|--------|------|
| 查看下级单位             | ✓    | ✓    | ✓      | ✓      | ✗    |
| 创建直属单位             | ✓    | ✓    | ✓      | ✓      | ✗    |
| 调整区域管辖关系         | ✓    | ✓    | ✓      | ✗      | ✗    |
| 发布全省标准             | ✓    | ✗    | ✗      | ✗      | ✗    |
| 查看跨区域数据           | ✓    | 本市  | 本区县  | 本学区 | 本校   |
| 审核下级实验记录         | ✓    | ✓    | ✓      | ✓      | ✗    |
| 配置系统参数             | ✓    | ✓    | ✓      | ✗      | ✗    |
| 查看全域数据看板         | ✓    | ✓    | ✗      | ✗      | ✗    |
| 发布全局通知             | ✓    | ✓    | ✓      | ✗      | ✗    |
| 执行跨级数据恢复         | ✓    | ✓    | ✗      | ✗      | ✗    |

  

（三）五级权限结构详细描述

#### 权限层级定义
```
第1级: 省级 (Province)
├── 管理范围: 全省所有市、区县、学区、学校
├── 直管学校: 10+所省直属学校
└── 权限级别: 最高级 (Level 1)

第2级: 市级 (City)
├── 管理范围: 本市所有区县、学区、学校
├── 直管学校: 10+所市直属学校
└── 权限级别: 高级 (Level 2)

第3级: 区县级 (District)
├── 管理范围: 本区县所有学区、学校
├── 直管学校: 10+所区县直属学校
└── 权限级别: 中级 (Level 3)

第4级: 学区级 (Education Zone)
├── 管理范围: 本学区所有学校
├── 直管学校: 10+所学区直属学校
└── 权限级别: 普通级 (Level 4)

第5级: 学校级 (School)
├── 管理范围: 仅本校
├── 直管学校: 无
└── 权限级别: 基础级 (Level 5)

未来扩展: 第6级 (预留)
├── 管理范围: 待定
├── 直管学校: 待定
└── 权限级别: 待定 (Level 6)
```

### 权限管理原则
1. **上级管下级原则**: 上级行政单位可以管理所有下级行政单位和学校
2. **直接管理原则**: 各级单位可以直接管理自己的直属学校
3. **学区限制原则**: 学区只能管理自己下属的学校，不能跨学区管理
4. **学校自治原则**: 学校只能管理自己的内部事务
5. **权限继承原则**: 下级继承上级的部分权限，但不能超越上级权限范围

### 用户角色定义
- **超级管理员**: 系统最高权限，可管理所有功能
- **省级管理员**: 省级权限，管理全省业务
- **市级管理员**: 市级权限，管理本市业务
- **区县管理员**: 区县级权限，管理本区县业务
- **学区管理员**: 学区级权限，管理本学区业务
- **学校管理员**: 学校级权限，管理本校业务
- **教师用户**: 基础权限，执行实验教学任务
- **学生用户**: 最低权限，参与实验学习和考试

## 开发优先级排序

### 第一阶段 (核心基础) - 优先级: 最高
1. **项目基础架构搭建**
   - Laravel后端项目初始化
   - Vue前端项目初始化
   - 数据库设计和迁移
   - 基础配置和环境搭建

2. **用户权限管理系统**
   - 用户认证 (注册/登录/注销)
   - 五级权限体系实现
   - 角色管理和权限分配
   - 单位组织架构管理

### 第二阶段 (核心业务) - 优先级: 高
3. **实验教学管理基础**
   - 实验课程基础管理
   - 实验记录基础功能
   - 基础数据字典管理

4. **设备管理基础**
   - 设备档案基础管理
   - 设备状态基础跟踪

### 第三阶段 (功能完善) - 优先级: 中
5. **实验考试系统**
   - 基础题库管理
   - 简单考试功能

6. **系统管理功能**
   - 日志记录
   - 基础报表

### 第四阶段 (功能增强) - 优先级: 低
7. **高级功能实现**
   - 复杂统计分析
   - 高级报表功能
   - 移动端适配
   - 接口开放

## 技术选型说明

### 前端技术选型理由
- **Vue 3**: 现代前端框架，组合式API，性能优秀
- **Element Plus**: 成熟的Vue 3 UI组件库，组件丰富
- **Pinia**: Vue 3官方推荐的状态管理库
- **Vite**: 快速的构建工具，开发体验好

### 后端技术选型理由
- **Laravel**: PHP生态最成熟的框架，文档完善
- **MySQL**: 稳定可靠的关系型数据库
- **Composer**: PHP标准的依赖管理工具

### 架构设计原则
- **前后端分离**: API驱动，便于维护和扩展
- **RESTful API**: 标准化接口设计
- **模块化设计**: 高内聚低耦合
- **可扩展性**: 支持未来功能扩展
- **安全性**: 完善的权限控制和数据验证

## 项目约束和风险

### 开发约束
- **开发者技能**: 非专业开发者，依赖AI辅助开发
- **对话限制**: AI对话数量有限，需要良好的文档管理
- **时间约束**: 个人项目，开发时间相对有限
- **资源约束**: 使用免费或低成本的开发工具和服务

### 主要风险
- **技术风险**: 对框架不够熟悉可能导致开发困难
- **进度风险**: AI对话限制可能影响开发连续性
- **质量风险**: 缺乏专业测试可能导致系统不稳定
- **维护风险**: 系统复杂度增加后维护困难

### 风险应对措施
- **文档先行**: 完善的文档体系确保开发连续性
- **模块化开发**: 降低系统复杂度，便于维护
- **渐进式开发**: 先实现核心功能，再逐步完善
- **标准化开发**: 遵循框架最佳实践，降低技术风险

所用数据库：在xampp中创建了数据库gcqets数据库，访问账号root,密码是：liningyu2000。
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=gcqets
DB_USERNAME=root
DB_PASSWORD=liningyu2000


## 下一步工作计划

### 立即执行 (本周)
1. 创建数据库设计文档
2. 创建API接口规范文档
3. 初始化Laravel后端项目
4. 初始化Vue前端项目

### 短期计划 (2-4周)
1. 完成用户认证系统
2. 实现五级权限管理
3. 完成用户管理CRUD功能
4. 完成单位组织管理功能

### 中期计划 (1-3个月)
1. 完成实验教学管理基础功能
2. 完成设备管理基础功能
3. 系统集成测试和优化

### 长期计划 (3-6个月)
1. 完善所有功能模块
2. 系统性能优化
3. 用户培训和部署上线

---

**备注**: 本文档将随着项目进展不断更新和完善，确保始终反映最新的项目状态和规划。