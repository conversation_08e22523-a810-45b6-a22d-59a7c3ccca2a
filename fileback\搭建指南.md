下面是一份详细的文档，记录了如何在F:\xampp\htdocs\gcqets目录下分别搭建前端（位于 `frontend` 文件夹）与后端（位于 `backend` 文件夹）的开发环境。
请根据各步骤说明，在对应文件夹中依次执行操作。你可以将下面的内容保存为 Markdown 文档，例如 `搭建指南.md`。
# 项目环境搭建指南
本指南主要针对以下技术栈进行环境安装与验证：
- **前端**：Node.js (v22.11.0)、Vue (v3.3.4)、Vue Router (v4.2.4)、Pinia (v2.1.6)、Element Plus (v2.9.5)、Axios (v1.4.0)、Vite (v4.5.9)  
- **后端**：PHP (v8.2.12)、<PERSON><PERSON> (v12.0.1)、MySQL/MariaDB (v10.4.32)、Composer (v2.6.10)
项目目录结构如下：  
F:\xampp\htdocs\gcets
 ├── frontend   // 前端项目文件夹
 └── backend    // 后端项目文件夹
```
## 第一部分：前端安装与验证
### 前提准备
   1. **Node.js 安装与验证**  
   请确认已经安装 Node.js。打开命令行（CMD、PowerShell 或 Windows Terminal），输入：
     node -v
   输出应为：  
     v22.11.0
   2. **确保 npm 可用**  
   检查 npm 版本：
   npm -v
   输出应为：  
   10.9.0
### 步骤 1：创建前端项目
   1. **进入前端文件夹**  
   在命令行中输入以下命令进入项目中的 `frontend` 目录：
   cd F:\xampp\htdocs\gcets\frontend
   2. **使用 Vite 创建 Vue 项目**  
   在 `frontend` 文件夹内执行命令，使用 Vite 创建基于 Vue 3 的项目。注意此处使用 “.” 让项目内容直接生成到该目录下（如果目录中已有文件，请先备份）：
   npm init vite@latest . -- --template vue
   系统会询问你确认创建项目，按照提示操作即可。 
   3. **安装项目依赖**  
   创建项目完成后，仍在 `F:\xampp\htdocs\gcets\frontend` 目录内，运行：
   npm install
   系统会询问你确认安装依赖，按照提示操作即可。
### 步骤 2：添加其他前端依赖
   根据你的技术栈需求，还需要安装 Vue Router、Pinia、Element Plus 以及 Axios。在 `frontend` 文件夹中依次运行：
npm install vue-router@4 pinia element-plus axios
这将下载并添加相关库到你的项目中。
      检查 package.json 在项目根目录下打开 package.json 文件，查看 dependencies 是否包含这些包。例如：
json
"dependencies": {
  "vue-router": "^4.x.x",
  "pinia": "^2.x.x",
  "element-plus": "^2.x.x",
  "axios": "^1.x.x"
}
使用 npm list 或 yarn list 命令 在终端运行以下命令：
sh
npm list vue-router pinia element-plus axios


### 步骤 3：启动并验证前端环境
1. **启动开发服务器**  
   确认仍在 `F:\xampp\htdocs\gcets\frontend` 文件夹，运行以下命令启动 Vite 开发服务器：
   npm run dev
   系统会询问你确认启动开发服务器，按照提示操作即可。

2. **访问项目页面**  
   根据命令行提示的地址（默认通常是 [http://localhost:5173](http://localhost:5173)），在浏览器中访问此 URL。  
   如果页面正常加载且控制台无错误，则说明前端环境已配置成功。
   
## 第二部分：后端安装与验证
### 前提准备
1. **PHP 与 Composer 检查**  
   - 打开命令行并输入验证 PHP 版本（假设 XAMPP 中已含有 PHP）：
     php -v
     输出应类似于：
     PHP 8.2.12 (cli) ...

   - 验证 Composer 版本：
     composer -V  
     输出示例：
     Composer version 2.6.10 ...

      如果没有安装，请根据官方文档进行安装。

### 步骤 1：创建 Laravel 后端项目
   1. **进入后端文件夹**  
   通过命令行进入 `backend` 文件夹：
   cd F:\xampp\htdocs\gcets\backend

2. **使用 Composer 创建 Laravel 项目**  
   在 `backend` 目录中执行以下命令创建 Laravel 项目（注意用 “.” 表示在当前目录创建项目文件，版本号使用 "12.*" 以确保安装对应的 Laravel 版本）：
   composer create-project --prefer-dist laravel/laravel . "12.*"
   这一步将下载 Laravel 框架及其依赖库到 `F:\xampp\htdocs\gcets\backend` 中。
### 步骤 2：配置数据库（MariaDB）
   1. **安装与启动 MariaDB**     
   如果你使用的是 XAMPP，则通常 Apache、MySQL/MariaDB 都已集成并可通过控制面板启动。确保 MySQL/MariaDB 服务处于启动状态。
   2. **修改 Laravel 数据库配置**  
   编辑 `F:\xampp\htdocs\gcets\backend` 中的 `.env` 文件，找到数据库配置项并修改为：
   DB_CONNECTION=mysql
   DB_HOST=127.0.0.1
   DB_PORT=3306
   DB_DATABASE=your_database_name      # 请替换为你新建的数据库名
   DB_USERNAME=your_database_username  # 数据库用户名，一般是 "root"（根据你 XAMPP 的设置）
   DB_PASSWORD=your_database_password  # 数据库密码，如无则留空
   确保在数据库管理工具（例如 phpMyAdmin）中创建对应的数据库。

3. **执行迁移验证数据库连接**  
   仍在 `backend` 文件夹内，通过运行以下命令测试数据库连接并执行默认迁移：
   php artisan migrate

   如果无报错信息且数据库中看到相关数据表，则说明 Laravel 与数据库配置正确。

### 步骤 3：启动并验证后端环境

1. **启动 Laravel 内置开发服务器**  
   在 `backend` 文件夹内运行下面命令启动服务器：
   php artisan serve

   默认显示地址一般为 [http://127.0.0.1:8000](http://127.0.0.1:8000)。

2. **访问 Laravel 欢迎页面**  
   打开浏览器，访问上述地址。若能看到 Laravel 的欢迎页面，并且控制台中无错误日志，则说明后端环境搭建成功。

## 附录：常用命令汇总

| **工具**             | **所在文件夹**                      | **命令**                                      | **说明**                                             |
|-------------------|------------------------------------|---------------------------------------------|----------------------------------------------------|
| **Node.js/npm**   | 任意（在命令行环境下可全局运行）           | `node -v` <br> `npm -v`                     | 检查 Node.js 和 npm 版本                              |
| **Vite 项目创建** | `F:\xampp\htdocs\gcets\frontend`   | `npm init vite@latest . -- --template vue`  | 在 frontend 文件夹中创建基于 Vue 3 的项目                |
| **安装依赖**      | `F:\xampp\htdocs\gcets\frontend`   | `npm install` <br> `npm install vue-router@4 pinia element-plus axios` | 安装项目依赖与其他插件                              |
| **启动前端服务**   | `F:\xampp\htdocs\gcets\frontend`   | `npm run dev`                               | 启动 Vite 开发服务器，验证前端环境                    |
| **Composer 与 PHP** | 任意（全局可运行，建议在 XAMPP 环境中验证） | `php -v` <br> `composer -V`                        | 检查 PHP 与 Composer 版本                             |
| **Laravel 项目创建** | `F:\xampp\htdocs\gcets\backend`    | `composer create-project --prefer-dist laravel/laravel . "12.*"` | 在 backend 文件夹中创建 Laravel 项目                |
| **数据库迁移**      | `F:\xampp\htdocs\gcets\backend`    | `php artisan migrate`                       | 执行数据库迁移以验证与数据库连接                      |
| **启动后端服务**    | `F:\xampp\htdocs\gcets\backend`    | `php artisan serve`                         | 启动 Laravel 内置服务器，验证后端环境                |


## 总结
- **前端部分**：  
  在 `F:\xampp\htdocs\gcets\frontend` 下，通过 Vite 创建 Vue 项目并安装依赖。运行 `npm run dev` 后，通过浏览器访问本地端口（默认 5173）验证页面是否正常显示。
- **后端部分**：  
  在 `F:\xampp\htdocs\gcets\backend` 下，使用 Composer 创建 Laravel 项目，随后配置 `.env` 文件中的数据库信息（确保 XAMPP 中的 MySQL/MariaDB 服务已启动并已创建目标数据库）。执行 `php artisan migrate` 验证数据库连接，再通过 `php artisan serve` 启动开发服务器，测试 [http://127.0.0.1:8000](http://127.0.0.1:8000) 是否能正确访问 Laravel 欢迎页。
按照上述步骤分别在对应文件夹下操作，即可成功搭建前端与后端环境并通过验证。若后续需要前后端联调，注意跨域设置（如 Laravel 中配置 Cors 中间件）和 API 请求地址的调整。
