功能模块详细说明
模块一：组织机构
	功能一：组织管理
		子功能一：组织列表：区县、学区、学校信息
			适用角色： 区县管理员
			功能说明： 查看、搜索、筛选组织列表。
		子功能二：新增组织：新增区县、学区、学校信息
			适用角色： 区县管理员
			功能说明： 添加新组织，填写组织基本信息并分配角色。区县管理员可以添加区县、学区和学校的基本信息，例如：名称、代码、联系方式等。
			提供详细的输入提示和校验，确保信息的准确性和完整性。
		子功能三：编辑组织：编辑区县、学区、学校信息
			适用角色： 区县管理员
			功能说明： 修改组织的基本信息和角色分配。 区县管理员可以标记学校是否为区县直属学校。
		子功能四：删除组织
			适用角色： 区县管理员
			功能说明： 删除组织，同时删除该组织下的所有用户和角色。
		子功能五：组织权限设置
			适用角色： 区县管理员
			功能说明： 设置组织之间的权限继承关系。	
		子功能六：学校信息批量导入
			适用角色： 区县管理员
			功能说明： 支持批量导入学校信息，例如通过 Excel 文件导入。
			优化建议： 提供详细的导入模板和错误提示，方便用户操作和排查错误。
		子功能七：学区划分管理
			适用角色： 区县管理员
			功能说明： 区县管理员可以进行学区划分管理，例如：根据地理位置、学校规模等因素自动或手动划分学区。
						
	功能二：用户管理 
		子功能一：用户列表
			适用角色： 区县管理员
			功能说明： 查看、搜索、筛选用户列表。
		子功能二：新增用户
			适用角色： 区县管理员
			功能说明： 添加新用户，填写用户基本信息并分配角色。
		子功能三：编辑用户
			适用角色： 区县管理员
			功能说明： 修改用户的基本信息和角色分配。
		子功能四：删除用户
			适用角色： 区县管理员
			功能说明： 删除用户账户。
	功能三：角色管理 
		子功能一：角色列表
			适用角色： 区县管理员
			功能说明： 查看、搜索、筛选角色列表。
		子功能二：新增角色
			适用角色： 区县管理员
			功能说明： 创建新角色，定义角色名称和描述。
		子功能三：编辑角色
			适用角色： 区县管理员
			功能说明： 修改角色的名称、描述和权限设置。
		子功能四：删除角色
			适用角色： 区县管理员
			功能说明： 删除不需要的角色。
		子功能五：权限设置
			适用角色： 区县管理员
			功能说明： 为角色分配功能按钮权限和数据访问权限。
		子功能六：权限继承设置
			适用角色： 区县管理员
			功能说明： 设置角色之间的权限继承关系。
		子功能七：权限撤销设置
			适用角色： 区县管理员
			功能说明： 撤销角色的特定权限。


模块二：基础数据
	功能一：实验目录管理
		子功能一：实验目录维护
			适用角色：区县管理员、学区管理员、学校管理员
			功能说明：支持按科目、年级、教材版本存储和管理实验目录信息，提供在线上传、编辑和维护最新实验目录的功能，支持版本控制和历史追溯，实现跨区域、跨学校的实验目录共享
			主要特点：
			 1.多维度分类管理: 
			  按科目（物理、化学、生物等）分类
			  按年级（小学、初中、高中）分类
			  按教材版本（人教版、教科版等）分类
			 2.目录版本管理 
			  支持历史版本追溯
			  提供版本对比功能
			  记录每次修改的详细日志
			 3.目录共享与授权 
			  区县教育部门可统一管理和发布标准目录
			  学校可根据教材版本选择适用的实验目录
			  支持目录的精细化权限控制
		子功能二：目录导入与校验
		    适用角色：区县管理员、学区管理员
		    功能说明：支持从教育局标准库导入实验目录，提供严格的数据格式校验机制，自动检测并处理数据导入冲突，支持Excel等多种格式的导入。
		    主要特点：
			 1.数据导入校验 
			  严格检查数据完整性
			  验证目录条目的规范性
			  自动识别并提示潜在冲突
			 2.导入冲突处理 
			  提供手动/自动冲突解决方案
			  记录每次导入的详细日志
			  支持回滚操作
	    子功能三：课程标准关联
		    适用角色：区县管理员、学区管理员
		    功能说明：将实验目录与课程标准精确关联，管理不同版本的课程标准，提供版本对比和切换功能。
		    主要特点：
			 1.课程标准版本管理 
			  记录课程标准历史版本
			  支持版本间详细对比
			  提供版本切换追溯功能
			  管理不同版本的课程标准
			  提供版本对比和切换功能
			 2.精确关联机制 
			  实验目录与课程标准精确映射
			  支持多维度关联（科目、年级、版本）
	    子功能四：照片类型配置
		    适用角色：区县管理员、学区管理员
		    功能说明：按教材版本和实验类型设置必传照片类型，提供自定义照片模板功能，确保实验记录的完整性和规范性。
		    主要特点：
			 1.照片类型灵活配置 
			  支持自定义必传照片类型
			  可按教材版本和实验类型差异化配置
			  提供照片模板管理
			 2.合规性检查 
			  自动检查照片类型和数量
			  提供照片合规性预警机制
		子功能五：实验目录表版本管理
			适用角色：区县管理员
			功能说明：按学科、年级、教材版本维护实验目录表，支持Excel导入和版本差异对比，提供目录表的版本追溯和管理。
			主要特点：
			 1.多维度目录管理 
			    精细化分类（学科、年级、教材版本）
				支持跨维度检索和管理
			 2.版本管理与对比 
				记录目录表历史版本
				提供版本间详细差异对比
				支持版本回滚和恢复
	功能二：设备物料管理
		子功能一：实验器材库存管理
			适用角色：区县管理员、学区管理员
			功能说明：管理器材入库/出库/盘点，设置安全库存量并触发预警。
		子功能二：耗材使用记录
			适用角色：区县管理员、学区管理员
            功能说明：记录耗材用量与实验记录的关联关系，生成消耗趋势报表。
        子功能三：设备借用记录
            适用角色：区县管理员、学区管理员
            功能说明：管理设备借还全流程，支持超期未还自动提醒。
         子功能四：设备归还记录
            适用角色：区县管理员、学区管理员
            功能说明：记录设备归还时的状态（完好/损坏），支持损坏责任追溯。
         子功能五：设备和耗材分类管理
            适用角色：区县管理员、学区管理员
            功能说明：按学科（物理/化学等）、用途（测量/加热等）建立分类标签体系。
模块三：实验管理
    功能一：实验计划申报
      子功能一：创建实验教学计划
        适用角色：教师、学校管理员
        功能说明：创建计划时关联教材版本和实验目录表条目，设置状态（草稿/待审批等）。
      子功能二：修改实验教学计划
        适用角色：教师、学校管理员
        功能说明：按权限修改计划（教师仅可修改草稿，管理员可修改多状态计划）。
      子功能三：关联课程标准和教材
        适用角色：教师、学校管理员
        功能说明：选择实验对应的课程标准版本和教材版本（如人教版一年级下册）。
      子功能四：查看审批结果
        适用角色：教师、学校管理员
        功能说明：展示审批意见详情，支持一键查看被驳回项的历史修改记录。
      子功能五：实验目录表关联排课
        适用角色：学校管理员
        功能说明：将实验目录表与学校课程表绑定，自动生成每周实验教学进度甘特图。
    功能二：实验记录填报
      子功能一：记录实验执行情况
        适用角色：教师
        功能说明：填写实验过程时需关联实验目录表条目，选择完成状态（未完成/部分完成/已完成）。
      子功能二：实时拍照上传
        适用角色：教师
        功能说明：移动端拍照时自动添加时间、地点水印，支持照片合规性预检（如模糊检测）。
      子功能三：本地上传照片
        适用角色：教师
        功能说明：上传本地照片时强制选择照片类型（过程/结果/器材准备）。
      子功能四：数据验证提示
        适用角色：教师
        功能说明：提交前自动检查必填项、照片类型和数量，提示缺失内容。
      子功能五：器材准备确认
        适用角色：教师
        功能说明：上传器材清点照片并标记使用数量，系统自动比对计划耗材量。
    功能三：实验记录审核
      子功能一：批量审核照片合规性
        适用角色：学校/学区管理员
        功能说明：批量审核时调用AI识别（如检测照片中是否出现对应实验器材）。
      子功能二：打回修改操作
        适用角色：学校/学区管理员
        功能说明：打回时需指定修改项（如"缺少器材准备照片"），支持附加语音备注。
      子功能三：强制完成操作
        适用角色：学校/学区管理员
        功能说明：标记异常完成记录时需填写强制完成原因（如"器材损坏无法补做"）。
      子功能四：审核意见详细记录
        适用角色：学校/学区管理员
        功能说明：记录审核人、时间、意见类型（格式问题/内容不符等），生成审核日志。
    功能四：实验日历
      子功能一：可视化实验安排展示
        适用角色：教师、学校管理员
        功能说明：日历视图按颜色区分实验状态（待执行/已完成/逾期），支持按学科筛选。
      子功能二：逾期实验预警提示
        适用角色：教师、学校管理员
        功能说明：可配置预警规则（如提前3天提醒未排课实验），支持短信/站内信通知。
      子功能三：实验详情查看
        适用角色：教师、学校管理员
        功能说明：查看实验关联的目录表条目、课程表时间、审核记录等全链路信息。
    功能五：实验执行监控
      子功能一：实验进度看板
        适用角色：区县/学校管理员
        功能说明：按教材版本展示各校完成率，用红黄绿灯标记落后于平均进度的学校。
      子功能二：异常实验分析
        适用角色：区县/学校管理员
        功能说明：自动检测异常数据（如同一器材多教室同时使用），生成疑似造假清单。
  

模块四：系统管理 
	功能一：消息中心
		子功能一：系统通知查看
			适用角色： 所有用户
			功能说明： 查看系统发布的通知公告。
			优化建议： 系统通知类型可以更丰富，例如：实验计划审批通知、督导任务通知、系统维护公告等。
		子功能二：待办提醒查看
			适用角色： 所有用户
			功能说明： 查看待办任务提醒，例如：待审批的实验计划、待审核的实验记录、待提交的督导报告等。
		子功能三：消息分类筛选
			适用角色： 所有用户
			功能说明： 按消息类型（系统通知、待办提醒等）筛选消息。
		子功能四：消息搜索
			适用角色： 所有用户
			功能说明： 支持按关键词搜索消息。
			优化建议： 可以考虑增加消息推送功能，例如：通过短信、APP 推送等方式及时提醒用户重要消息。
	功能二：移动端适配
		子功能一：照片上传优化功能
			适用角色： 教师
			功能说明： 针对移动端优化照片上传功能，例如：压缩上传、断点续传、批量上传等，提升用户体验。
		子功能二：快速填报入口
			适用角色： 教师
			功能说明： 在移动端提供快速填报实验记录的入口，例如：快捷功能、常用操作按钮等，方便教师快速记录实验数据。
		子功能三：离线使用模式
			适用角色： 教师
			功能说明： 支持离线填报实验记录，在网络不稳定或无网络环境下，教师仍然可以进行实验记录填写，联网后自动同步数据。
			优化建议： 离线使用模式可以更完善，例如：离线状态下可以查看已有的实验计划和部分数据，而不仅仅是填报记录。
	功能三：照片策略配置
		子功能一：设置照片大小限制
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置上传照片的最大大小限制，例如：2MB、5MB等，以控制系统存储空间占用。
		子功能二：文件存储路径管理
			适用角色： 区县管理员
			功能说明： 区县管理员可以管理系统文件的存储路径，例如：照片存储路径、附件存储路径等。
			优化建议： 文件存储路径可以支持多种存储方式，例如：本地存储、云存储等。
		子功能三：照片水印设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置照片水印内容和位置，例如：水印文字、水印透明度、水印位置等，用于保护照片版权或标识照片来源。
			优化建议： 水印设置可以更灵活，例如：水印内容、水印位置、水印透明度等可以自定义，并支持预览水印效果。
	功能四：分析模型配置
		子功能一：自定义统计指标
			适用角色： 区县管理员
			功能说明： 区县管理员可以自定义实验数据统计指标，例如：自定义计算公式、统计维度等。
			优化建议： 提供常用的统计指标模板，方便用户快速选择和配置，例如：完成率、达标率、优秀率、合格率等。
		子功能二：预警阈值设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置异常数据预警阈值，例如：设置实验完成率低于多少时触发预警。
		子功能三：模型导入
			适用角色： 区县管理员
			功能说明： 区县管理员可以导入预先训练好的分析模型，用于更高级的数据分析和预测。
			优化建议： 模型导入导出可以支持通用的模型格式，例如：PMML、NNX等，方便模型共享和迁移。
		子功能四：模型导出
			适用角色： 区县管理员
			功能说明： 区县管理员可以将系统中配置的分析模型导出，用于备份或共享。
	功能五：数据备份与恢复
		子功能一：自动备份设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置系统自动备份功能，包括设置备份频率（每天、每周、每月）、备份时间、备份文件存储路径等。
			优化建议： 自动备份设置可以增加备份时间段设置，避开系统高峰期进行备份，减少对系统性能的影响。
		子功能二：手动备份操作
			适用角色： 区县管理员
			功能说明： 区县管理员可以手动触发数据备份操作，例如：在系统升级前、重要数据修改前进行手动备份。
		子功能三：备份文件存储路径设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置备份文件的存储路径，例如：本地磁盘路径、网络存储路径等。
			优化建议： 备份可以考虑异地备份，将备份文件存储到不同的物理位置，提高数据安全性。
		子功能四：备份频率设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置数据备份的频率，例如：每天、每周、每月。
		子功能五：从备份文件恢复数据
			适用角色： 区县管理员
			功能说明： 区县管理员可以从之前备份的文件中恢复系统数据，用于数据灾难恢复或系统回滚。
			优化建议： 数据恢复可以支持部分数据恢复，例如：只恢复特定时间段或特定模块的数据，提高恢复效率。


模块五：数据看板
	功能一：学校实验统计
		子功能一：完成率统计
			适用角色： 学校管理员、学区管理员
			功能说明： 统计本校或本学区学校的实验计划完成率和实验记录提交率。
			优化建议： 增加时间维度筛选，例如：按学期、按月份、按周等查看统计数据，方便进行趋势分析。
		子功能二：达标率统计
			适用角色： 学校管理员、学区管理员
			功能说明： 统计实验记录的审核达标率。
			优化建议： 增加时间维度筛选，与完成率统计保持一致。
		子功能三：照片完整性分析
			适用角色： 学校管理员、学区管理员
			功能说明： 分析实验照片上传的完整性，例如：统计每个实验记录平均上传照片数量，照片缺失率等。
			优化建议： 可以根据预设的“必传照片类型”进行完整性分析。
		子功能四：实验类型分类统计
			适用角色： 学校管理员、学区管理员
			功能说明： 按实验类型（例如：物理实验、化学实验、生物实验等）进行分类统计实验计划数量、完成率、达标率等数据。
			优化建议： 增加时间维度筛选，与完成率统计保持一致。
	功能二：学区实验监控
		子功能一：下属学校对比分析
			适用角色： 学区管理员
			功能说明： 对比分析本学区下属学校的实验数据，例如：完成率、达标率、照片完整性等。
			优化建议： 增加对比维度选择，允许学区管理员自定义选择需要对比的指标。
	       子功能二：异常数据预警
                       适用角色： 学区管理员
	               功能说明： 对异常实验数据进行预警，例如：实验完成率过低、达标率过低、照片完整性差等。
			优化建议： 预警规则可以更具体，例如：完成率低于80%预警、照片上传率低于50%预警等，并提供预警原因说明。
		子功能三：实验进度实时监控
			适用角色： 学区管理员
			功能说明： 实时监控本学区学校的实验进度，以图表或列表形式展示各学校的实验计划执行情况和实验记录填报情况。
			优化建议： 监控界面可以更直观，例如：使用进度条或进度图表展示实验进度，方便学区管理员快速了解各学校的实验进度。
	功能三：全域实验大屏
		子功能一：全区数据地图可视化展示
			适用角色： 区县管理员
			功能说明： 以地图形式可视化展示全区实验数据，例如：各学区实验完成率、达标率等指标在地图上以颜色深浅进行展示。
			优化建议： 地图展示效果可以更丰富，例如：颜色深浅代表指标高低，点击地图区域可以下钻查看该区域的详细数据。
                子功能二：学区排名展示
			适用角色： 区县管理员
			功能说明： 展示全区各学区的实验数据排名，例如：按完成率、达标率等指标进行排名。
			优化建议： 增加时间维度筛选，与全区数据地图保持一致。
		子功能三：学校排名展示
			适用角色： 区县管理员
			功能说明： 展示全区各学校的实验数据排名，例如：按完成率、达标率等指标进行排名。
			优化建议： 增加时间维度筛选，与全区数据地图保持一致。
		子功能四：数据钻取查看
			适用角色： 区县管理员
			功能说明： 支持从区县 -> 学区 -> 学校的逐级数据钻取查看，方便区县管理员深入了解各层级的实验数据情况。
			优化建议： 数据钻取可以更灵活，例如：支持自定义选择钻取的层级和指标，不局限于固定层级路径。
		子功能五：数据钻取查看
			适用角色： 区县管理员
			功能说明： 支持从区县 -> 学区 -> 学校的逐级数据钻取查看，方便区县管理员深入了解各层级的实验数据情况。
			优化建议： 数据钻取可以更灵活，例如：支持自定义选择钻取的层级和指标，不局限于固定层级路径。
模块六：督导管理
	功能一：督导任务下发
		子功能一：创建检查任务
			适用角色： 学区管理员、区县管理员
			功能说明： 创建新的督导检查任务，包括填写任务名称、任务描述、督导范围、任务时间限制、任务优先级等信息。
			优化建议： 增加督导范围设置，允许指定督导任务针对某些学校、某些学区或全区县所有学校。
		子功能二：分配督导人员
			适用角色： 学区管理员、区县管理员
			功能说明： 为创建的督导任务分配具体的督导人员。
			优化建议： 支持批量分配督导人员。
		子功能三：设置任务时间限制
			适用角色： 学区管理员、区县管理员
			功能说明： 设置督导任务的开始时间和结束时间。
			优化建议： 增加任务时间限制设置，例如：任务开始时间、任务结束时间、任务持续时间等。
		子功能四：设置任务优先级
			适用角色： 学区管理员、区县管理员
			功能说明： 设置督导任务的优先级，例如：高、中、低。
		子功能五：设置督导内容模板
			适用角色： 学区管理员、区县管理员
			功能说明： 选择或自定义督导检查的内容模板，预设一些常用的督导检查项，方便快速创建督导任务。
	功能二：督导报告提交
		子功能一：上传督导记录
			适用角色： 督导人员
			功能说明： 督导人员上传督导过程中记录的内容，可以是文字描述、照片、附件等形式。
			优化建议： 支持多种格式的文件上传，例如：wrd、excel、pdf、图片等。
		子功能二：附加整改意见
			适用角色： 督导人员
			功能说明： 为督导报告附加整改意见，针对发现的问题提出具体的整改要求和建议。
			优化建议： 整改意见可以考虑结构化输入，例如：整改项、整改要求、整改期限等，方便后续跟踪整改情况。
		子功能三：使用报告模板
			适用角色： 督导人员
			功能说明： 督导人员可以使用预设的督导报告模板，快速生成规范化的督导报告。
	功能三：督导报告审核
		子功能一：审核流程配置
			适用角色： 学校管理员、学区管理员
			功能说明： 配置督导报告的审核流程，例如：一级审核、二级审核等。
			优化建议： 审核流程可以支持自定义配置，例如：可以设置多级审核流程，并指定每级审核人员角色。
		子功能二：整改闭环跟踪
			适用角色： 学校管理员、学区管理员
			功能说明： 对督导报告中提出的整改意见进行闭环跟踪，记录整改任务的下发、整改措施、整改结果、整改复核等情况，确保整改任务落实到位。
		子功能三：审核进度实时跟踪
			适用角色： 学校管理员、学区管理员
			功能说明： 实时跟踪督导报告的审核进度，了解当前审核环节和审核状态。
模块七：资源共享
	功能一：实验资源分享
		子功能一：上传实验教学案例
			适用角色： 教师、学校管理员
			功能说明： 教师和学校管理员可以上传优秀的实验教学案例，供其他教师参考学习。
			优化建议： 上传资源时需要进行资源分类，例如：按学科、年级、实验类型等进行分类。
		子功能二：上传实验指导文档
			适用角色： 教师、学校管理员
			功能说明： 教师和学校管理员可以上传实验指导文档，例如：实验操作手册、实验注意事项等。
			优化建议： 与教学案例一样，上传资源时需要进行资源分类。
		子功能三：上传实验视频
			适用角色： 教师、学校管理员
			功能说明： 教师和学校管理员可以上传实验教学视频，例如：实验操作演示、实验讲解视频等。
			优化建议： 与教学案例一样，上传资源时需要进行资源分类。
		子功能四：资源审核
			适用角色： 学区管理员、区县管理员
			功能说明： 增加资源审核机制，确保共享资源的质量和合规性，可以由学区管理员或区县管理员进行审核。
	功能二：资源搜索与筛选
		子功能一：关键词搜索
			适用角色： 教师、学校管理员
			功能说明： 用户可以通过关键词搜索需要的实验教学资源。
		子功能二：按学科筛选
			适用角色： 教师、学校管理员
			功能说明： 用户可以按学科筛选实验教学资源。
		子功能三：按实验类型筛选
			适用角色： 教师、学校管理员
			功能说明： 用户可以按实验类型筛选实验教学资源。
		子功能四：资源评价
			适用角色： 教师、学校管理员
			功能说明： 用户可以对下载或使用过的资源进行评分和评论，方便其他用户参考。
模块八：用户反馈
		功能一：用户意见反馈
			子功能一：提交反馈
			适用角色： 所有用户
			功能说明： 用户可以提交对系统的建议和意见。
		子功能二：问题报告
			适用角色： 所有用户
			功能说明： 用户可以报告系统使用中遇到的问题。
		子功能三：满意度调查
			适用角色： 所有用户
			功能说明： 定期进行用户满意度调查，收集用户对系统的评价。
模块九：通知管理
		功能一：通知发布
		    子功能一：发布通知
			适用角色： 区县管理员
			功能说明： 管理员可以发布通知公告。
		    子功能二：通知分类
			适用角色： 区县管理员
			功能说明： 可以对通知进行分类，例如：系统公告、实验计划通知、督导任务通知等。
		    子功能三：通知推送
			适用角色： 区县管理员
			功能说明： 支持通过短信、邮件、APP 推送等方式发送通知。
模块十：数据备份与恢复
		功能一：数据备份
			子功能一：自动备份设置
		子功能一：自动备份设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置系统自动备份功能，包括设置备份频率（每天、每周、每月）、备份时间、备份文件存储路径等。
			优化建议： 自动备份设置可以增加备份时间段设置，避开系统高峰期进行备份，减少对系统性能的影响。
		子功能二：手动备份操作
			适用角色： 区县管理员
			功能说明： 区县管理员可以手动触发数据备份操作，例如：在系统升级前、重要数据修改前进行手动备份。
		子功能三：备份文件存储路径设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置备份文件的存储路径，例如：本地磁盘路径、网络存储路径等。
			优化建议： 备份可以考虑异地备份，将备份文件存储到不同的物理位置，提高数据安全性。
		子功能四：备份频率设置
			适用角色： 区县管理员
			功能说明： 区县管理员可以设置数据备份的频率，例如：每天、每周、每月。
	功能二：数据恢复
		子功能一：从备份文件恢复数据
		子功能一：从备份文件恢复数据
			适用角色： 区县管理员
			功能说明： 区县管理员可以从之前备份的文件中恢复系统数据，用于数据灾难恢复或系统回滚。
			优化建议： 数据恢复可以支持部分数据恢复，例如：只恢复特定时间段或特定模块的数据，提高恢复效率。


以后的扩展模块：

模块十一：智能辅助
    功能一：器材智能推荐
      子功能一：替代方案提示
        适用角色：教师
        功能说明：当库存不足时，推荐替代器材（如"天平缺货，建议改用弹簧秤"）。
      子功能二：危险操作预警
        适用角色：教师
        功能说明：识别高风险实验步骤（如加热易燃物），弹出安全操作指引。
    功能二：AI实验报告生成
      子功能一：自动生成报告
        适用角色：教师
        功能说明：基于图文记录生成标准化报告，自动提取关键数据（如实验时长、参与人数）。
      子功能二：完整性检查
        适用角色：教师
        功能说明：检查实验步骤缺失（如"溶解实验未记录搅拌次数"），提示补充内容。
模块十二：移动端扩展
    功能一：扫码器材管理
      子功能一：快速登记
        适用角色：教师
        功能说明：扫描器材二维码自动登记使用记录，同步更新库存数据。
      子功能二：器材合规检查
        适用角色：教师
        功能说明：拍照识别器材型号是否与计划一致（如"检测到磁铁规格与目录表不符"）。
    功能二：离线模式
      子功能一：离线记录实验
        适用角色：教师
        功能说明：无网络时记录实验数据，联网后自动同步并校验时间戳连续性。
      子功能二：防作弊校验
        适用角色：系统自动
        功能说明：通过GPS定位确认实验执行地点，比对教室IP地址防止虚假记录。
注：所有修改均围绕以下核心目标：
精准监管：通过实验目录表版本绑定、器材清点验证等手段确保数据真实性
智能减负：AI报告生成、替代方案推荐等功能降低教师文书工作量
动态预警：从库存预警到实验进度预警形成全流程风险防控机制