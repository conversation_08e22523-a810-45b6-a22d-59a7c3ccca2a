# 实验教学管理系统 - 五级权限管理功能模块设计

## 模块一：组织机构管理

### 功能一：组织管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **组织列表**       | 省/市/区县/学区管理员     | 查看管理范围内的组织树，支持五级结构展示      |
| **新增组织**       | 省/市/区县/学区管理员     | 上级可创建下级单位，自动继承层级关系          |
| **编辑组织**       | 省/市/区县/学区管理员     | 修改组织信息并标记直属关系                  |
| **删除组织**       | 省/市/区县/学区管理员     | 级联删除需验证下级单位为空                  |
| **权限继承设置**   | 省/市/区县管理员          | 配置五级权限继承链：省→市→区县→学区→学校    |
| **批量导入**       | 省/市/区县/学区管理员     | 支持Excel导入多级单位，模板含五级层级字段   |
| **区域划分管理**   | 省/市/区县管理员          | 跨层级调整管辖关系，支持拖拽式区域划分      |

### 功能二：用户管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **用户列表**       | 省/市/区县/学区/学校管理员| 按管辖范围筛选用户                   |
| **新增用户**       | 省/市/区县/学区/学校管理员| 用户自动继承组织层级属性             |
| **编辑用户**       | 省/市/区县/学区/学校管理员| 修改用户信息及权限范围              |
| **删除用户**       | 省/市/区县/学区/学校管理员| 删除用户账户                          |
| **用户导入**       | 省/市/区县/学区管理员     | 批量导入用户并分配组织                |

### 功能三：角色管理
| 子功能             | 适用角色              | 功能说明                                  |
|--------------------|-----------------------|-----------------------------------------|
| **角色列表**       | 省/市/区县管理员      | 查看系统角色及其权限范围                |
| **新增角色**       | 省/市/区县管理员      | 创建新角色并指定适用层级                 |
| **编辑角色**       | 省/市/区县管理员      | 修改角色权限设置                        |
| **删除角色**       | 省/市/区县管理员      | 删除不需要的角色                        |
| **权限设置**       | 省/市/区县管理员      | 按层级分配数据权限                      |
| **权限继承设置**   | 省/市/区县管理员      | 配置跨层级权限继承关系                   |
| **权限撤销设置**   | 省/市/区县管理员      | 撤销特定层级的权限                      |

## 模块二：基础数据管理

### 功能一：实验目录管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **目录维护**       | 省/市/区县/学区/学校管理员| 多层级目录管理，省级可发布全省标准目录   |
| **目录导入**       | 省/市/区县管理员          | 支持导入上级标准目录                        |
| **课程标准关联**   | 省/市/区县管理员          | 关联省级课程标准                           |
| **照片类型配置**   | 省/市/区县管理员          | 设置各层级必传照片类型                     |
| **版本管理**       | 省/市/区县管理员          | 维护五级目录版本体系                       |

### 功能二：设备物料管理
| 子功能             | 适用角色                  | 功能说明                                  |
|--------------------|---------------------------|-----------------------------------------|
| **器材库存管理**   | 区县/学区/学校管理员      | 五级库存视图管理                         |
| **耗材使用记录**   | 区县/学区/学校管理员      | 按层级汇总耗材使用                       |
| **设备借用记录**   | 区县/学区/学校管理员      | 跨层级设备借用管理                       |
| **设备归还记录**   | 区县/学区/学校管理员      | 损坏设备跨级追溯                        |
| **分类管理**       | 区县/学区管理员           | 建立五级分类体系                       |

## 模块三：实验过程管理

### 功能一：实验计划申报
*适用角色：教师/学校管理员*
- 创建计划时自动关联层级目录版本
- 多级审批流程（学校→学区→区县→市→省）

### 功能二：实验记录填报
*适用角色：教师*
- 照片上传带五级位置水印
- 自动校验层级合规性

### 功能三：实验记录审核
| 审核层级          | 审核范围                |
|-------------------|-------------------------|
| 学校管理员        | 本校记录                |
| 学区管理员        | 本学区所有学校          |
| 区县管理员        | 本区县所有学区/直管学校 |
| 市级管理员        | 本市所有区县/直管学校   |
| 省级管理员        | 全省抽样检查            |

### 功能四：实验日历
*适用角色：教师/学校管理员*
- 五级实验进度可视化
- 逾期预警按层级通知

### 功能五：实验执行监控
| 监控层级          | 监控范围                |
|-------------------|-------------------------|
| 学校管理员        | 本校实验进度            |
| 学区管理员        | 本学区学校对比          |
| 区县管理员        | 本区县学区排名          |
| 市级管理员        | 本市区县对比            |
| 省级管理员        | 全省综合分析            |

## 模块四：系统管理

### 功能一：消息中心
- 层级化消息推送（省→市→区县→学区→学校）
- 待办事项按管辖范围分类

### 功能二：移动端适配
- 五级位置自动标记
- 离线模式支持层级数据同步

### 功能三：照片策略配置
*适用角色：省/市/区县管理员*
- 按层级设置不同照片策略
- 水印内容包含完整五级路径

### 功能四：分析模型配置
- 层级化统计指标（省级指标/市级指标等）
- 多级预警阈值设置

### 功能五：数据备份与恢复
*适用角色：省/市/区县管理员*
- 层级化备份策略
- 跨级数据恢复权限控制

## 模块五：数据看板

### 功能一：学校实验统计
- 按五级结构钻取完成率
- 层级对比分析

### 功能二：学区实验监控
- 学区下属学校对比
- 异常数据跨层级追溯

### 功能三：全域实验大屏
- 五级数据地图可视化
- 省→市→区县→学区→学校钻取
- 跨层级排名分析

## 模块六：督导管理

### 功能一：督导任务下发
- 五级任务分配体系
- 督导范围树形选择器

### 功能二：督导报告提交
- 报告自动标记五级路径
- 整改意见层级传递

### 功能三：督导报告审核
- 多级审核流程配置
- 整改闭环跨层级跟踪

## 模块七：资源共享

### 功能一：实验资源分享
*适用角色：教师/学校管理员*
- 资源按五级分类体系组织
- 省级优质资源优先展示

### 功能二：资源搜索与筛选
- 五级分类筛选
- 层级化资源评分体系

## 模块八：用户反馈

### 功能一：用户意见反馈
- 问题按五级分类
- 反馈处理进度层级通知

## 模块九：通知管理

### 功能一：通知发布
*适用角色：省/市/区县管理员*
- 层级化通知发布
- 精准推送目标组织

## 模块十：数据备份与恢复

### 功能一：数据备份
*适用角色：省/市/区县管理员*
- 层级化备份策略
- 定时备份计划

### 功能二：数据恢复
*适用角色：省/市/区县管理员*
- 按层级恢复数据
- 恢复权限验证

---

## 权限控制矩阵

| 操作权限                 | 省级 | 市级 | 区县级 | 学区级 | 校级 |
|--------------------------|------|------|--------|--------|------|
| 查看下级单位             | ✓    | ✓    | ✓      | ✓      | ✗    |
| 创建直属单位             | ✓    | ✓    | ✓      | ✓      | ✗    |
| 调整区域管辖关系         | ✓    | ✓    | ✓      | ✗      | ✗    |
| 发布全省标准             | ✓    | ✗    | ✗      | ✗      | ✗    |
| 查看跨区域数据           | ✓    | 本市 | 本区县 | 本学区 | 本校 |
| 审核下级实验记录         | ✓    | ✓    | ✓      | ✓      | ✗    |
| 配置系统参数             | ✓    | ✓    | ✓      | ✗      | ✗    |
| 查看全域数据看板         | ✓    | ✓    | ✗      | ✗      | ✗    |
| 发布全局通知             | ✓    | ✓    | ✓      | ✗      | ✗    |
| 执行跨级数据恢复         | ✓    | ✓    | ✗      | ✗      | ✗    |

> **文档版本**：v1.0  
> **最后更新**：2025年6月17日  
> **文档状态**：审核通过  

此文档已将所有功能模块适配五级权限管理体系，可直接用于后续开发。